<template>
  <!-- 精准劝导概览 -->
  <div class="handle-statistics">
    <div class="panel-header">
      <div class="title">
        <span
          class="title-text"
          style="cursor: pointer"
          @click="handleStatisticsClick"
        >
          精准劝导概览
        </span>
        <div class="time-controls">
          <TimeRangeSelector
            v-model="currentRange"
            :enabled-ranges="['month', 'year']"
            :custom-labels="{ month: '月', year: '年' }"
            :disable-future="true"
            :initial-month="selectedMonthDate"
            :initial-year="selectedYearDate"
            @change="handleRangeChange"
            @month-change="handleMonthDateChange"
            @year-change="handleYearDateChange"
          />
        </div>
      </div>
    </div>

    <div class="stat-info">
      <div class="info-group">
        <div class="info-item">
          <div class="label-box">
            <div class="icon-wrapper warning">
              <el-icon class="icon"><Bell /></el-icon>
            </div>
            <span class="label">任务总数</span>
          </div>
          <span class="value">{{ data.total }}</span>
        </div>
        <div class="info-item">
          <div class="label-box">
            <div class="icon-wrapper success">
              <el-icon class="icon"><Select /></el-icon>
            </div>
            <span class="label">总完成数</span>
          </div>
          <span class="value success">{{ data.handled }}</span>
        </div>
      </div>
    </div>
    <div ref="chartRef" class="stat-chart" @click="handleChartClick" />

    <!-- 使用新的弹窗组件 -->
    <HandleDetailDialog
      v-model="showDetailDialog"
      :initial-area="[]"
      :initial-time-range="currentRange"
      @closed="handleDialogClosed"
    />
  </div>
</template>

<script setup lang="ts" name="HandleStatistics">
import { Bell, Select } from "@element-plus/icons-vue";
import type { EChartsOption } from "echarts";
import * as echarts from "echarts";
import { onUnmounted, ref, watch, onMounted, computed } from "vue";
import dayjs from "dayjs";
import { useRouter } from "vue-router";
import TimeRangeSelector from "@/components/TimeRangeSelector.vue";
import HandleDetailDialog from "../dialogs/HandleDetailDialog.vue";
import type { TimeRangeType } from "@/components/TimeRangeSelector.vue";

const router = useRouter();

// 数据类型定义（适配hooks返回的数据结构）
const props = defineProps<{
  data: {
    total: number;
    handled: number;
    pending: number;
    rate: number;
    onTimeProcessingRate: number;
    trend: {
      warning: number[];
      handled: number[];
      dates: string[];
    };
  };
}>();

// 计算属性：将handled映射为completed
const completed = computed(() => props.data.handled);

const chartRef = ref<HTMLElement>();
let chart: echarts.ECharts | null = null;

const emit = defineEmits<{
  (
    e: "rangeChange",
    params: {
      range: string;
      componentName: string;
      year: string;
      startTime?: string;
      endTime?: string;
    }
  ): void;
}>();

// 时间范围选项
const timeRanges = [
  {
    label: "月",
    value: "month" as TimeRangeType
  },
  {
    label: "年",
    value: "year" as TimeRangeType
  }
];

// 当前选择的时间范围
const currentRange = ref<TimeRangeType>("month");

// 年份选择器
const selectedYear = ref(dayjs().format("YYYY"));
const selectedYearDate = ref(dayjs().format("YYYY"));

// 月份选择器（默认显示上个月）
const selectedMonthDate = ref(dayjs().subtract(1, "month").format("YYYY-MM"));

// 弹窗状态
const showDetailDialog = ref(false);

// 处理图表点击事件
const handleChartClick = () => {
  showDetailDialog.value = true;
};

// 处理弹窗关闭事件
const handleDialogClosed = () => {
  // 弹窗关闭后，重新渲染图表
  if (chart) {
    renderChart();
  }
};

// 处理年份变化
const handleYearDateChange = (year: string) => {
  selectedYear.value = year;
  selectedYearDate.value = year;
  emitRangeChange();
};

// 处理月份变化
const handleMonthDateChange = (month: string) => {
  selectedMonthDate.value = month;
  emitRangeChange();
};

// 处理时间范围切换
const handleRangeChange = (value: TimeRangeType) => {
  currentRange.value = value;
  // 切换时间范围时重置选中的日期
  if (value === "year") {
    selectedYearDate.value = dayjs().format("YYYY");
  } else {
    selectedMonthDate.value = dayjs().subtract(1, "month").format("YYYY-MM");
  }
  // 切换时间范围后立即发起请求
  emitRangeChange();
};

// 发送范围变化事件
const emitRangeChange = () => {
  const params: {
    range: TimeRangeType;
    componentName: string;
    year: string;
    startTime?: string;
    endTime?: string;
  } = {
    range: currentRange.value,
    componentName: "HandleStatistics",
    year: selectedYear.value
  };

  if (currentRange.value === "month") {
    const date = dayjs(selectedMonthDate.value);
    params.startTime = date.startOf("month").format("YYYY-MM-DD");
    params.endTime = date.endOf("month").format("YYYY-MM-DD");
  }

  emit("rangeChange", params);
};

// 获取图表的X轴数据
const getChartXAxisData = () => {
  if (!props.data?.trend || props.data.trend.dates.length === 0) {
    return [];
  }

  // 根据时间范围类型格式化X轴数据，去掉前导零
  return props.data.trend.dates.map(date => {
    if (currentRange.value === "year") {
      // 年份视图：处理月份数据，去掉前导零（01月 -> 1月）
      if (date.includes("月")) {
        // 如果已经是"XX月"格式，提取月份数字并去掉前导零
        const monthMatch = date.match(/(\d+)月/);
        if (monthMatch) {
          const monthNum = parseInt(monthMatch[1]);
          return `${monthNum}月`;
        }
      } else {
        // 如果是其他格式（如"2024-01"），使用dayjs格式化
        try {
          return dayjs(date).format("M月");
        } catch {
          return date; // 格式化失败时返回原值
        }
      }
    } else {
      // 月份视图：处理日期数据，去掉前导零（01日 -> 1日）
      if (date.includes("号")) {
        // 如果已经是"XX日"格式，提取日期数字并去掉前导零
        const dayMatch = date.match(/(\d+)号/);
        if (dayMatch) {
          const dayNum = parseInt(dayMatch[1]);
          return `${dayNum}号`;
        }
      } else {
        // 如果是其他格式（如"2024-01-01"或纯数字"01"），使用dayjs格式化
        try {
          // 先尝试作为完整日期解析
          if (date.includes("-")) {
            return dayjs(date).format("D号");
          } else {
            // 纯数字格式，直接转换
            const dayNum = parseInt(date);
            return `${dayNum}号`;
          }
        } catch {
          return date; // 格式化失败时返回原值
        }
      }
    }

    return date; // 默认返回原值
  });
};

// 渲染图表
const renderChart = () => {
  if (
    chartRef.value &&
    props.data?.trend &&
    props.data.trend.dates.length > 0
  ) {
    if (chart) {
      chart.dispose();
    }
    chart = echarts.init(chartRef.value);

    const xAxisData = getChartXAxisData();
    const totalData = props.data.trend.warning;
    const completedData = props.data.trend.handled;

    const option: EChartsOption = {
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow"
        },
        formatter: function (params: any) {
          let result = `${params[0].axisValue}<br/>`;
          params.forEach((param: any) => {
            result += `${param.marker}${param.seriesName}: ${param.value}<br/>`;
          });
          return result;
        },
        // 优化tooltip位置，防止被遮挡
        confine: true, // 限制tooltip在图表容器内
        backgroundColor: "rgba(0, 24, 75, 0.9)",
        borderColor: "rgba(64, 158, 255, 0.3)",
        borderWidth: 1,
        padding: [6, 8], // 减小内边距，让tooltip更紧凑
        textStyle: {
          color: "rgba(255, 255, 255, 0.9)",
          fontSize: 12 // 减小字体大小，让tooltip更小巧
        },
        position: function (point, params, dom, rect, size) {
          // 智能位置计算，防止tooltip超出边界
          const [mouseX, mouseY] = point;
          const { contentSize, viewSize } = size;
          const [tooltipWidth, tooltipHeight] = contentSize;
          const [containerWidth, containerHeight] = viewSize;

          let x = mouseX + 10; // 默认在鼠标右侧
          let y = mouseY - tooltipHeight / 2; // 垂直居中

          // 检查右边界，如果超出则显示在左侧
          if (x + tooltipWidth > containerWidth) {
            x = mouseX - tooltipWidth - 10;
          }

          // 检查上下边界
          if (y < 0) {
            y = 10;
          } else if (y + tooltipHeight > containerHeight) {
            y = containerHeight - tooltipHeight - 10;
          }

          return [x, y];
        }
      },
      legend: {
        data: ["任务总数", "总完成数"],
        textStyle: {
          color: "rgba(255, 255, 255, 0.7)",
          fontSize: 12 // 图表右上角图例文字字体大小
        },
        top: 0,
        right: 10
      },
      dataZoom: [
        {
          type: "inside",
          start: 0,
          end: 100,
          zoomOnMouseWheel: true,
          moveOnMouseMove: true,
          moveOnMouseWheel: false
        },
        {
          type: "slider",
          start: 0,
          end: 100,
          bottom: 10,
          height: 20,
          handleStyle: {
            color: "#409eff"
          },
          fillerColor: "rgba(64, 158, 255, 0.2)",
          backgroundColor: "rgba(0, 0, 0, 0.3)",
          borderColor: "rgba(64, 158, 255, 0.3)",
          textStyle: {
            color: "rgba(255, 255, 255, 0.6)",
            fontSize: 10 // 图表底部滑块(dataZoom)文字字体大小
          }
        }
      ],
      grid: {
        top: 35,
        right: 30,
        bottom: 50,
        left: currentRange.value === "month" ? 30 : 20, // 动态适应：月模式左边距30px，年模式左边距20px
        containLabel: true
      },
      xAxis: {
        type: "category",
        data: xAxisData,
        axisLine: {
          lineStyle: {
            color: "rgba(255, 255, 255, 0.3)"
          }
        },
        axisLabel: {
          color: "rgba(255, 255, 255, 0.6)",
          fontSize: 14, // X轴底部时间标签字体大小
          interval: currentRange.value === "month" ? 2 : 0, // 动态适应：月模式显示间隔2，年模式显示全部
          margin: 8,
          rotate: currentRange.value === "month" ? 30 : 0 // 动态适应：月模式旋转30度，年模式不旋转
        }
      },
      yAxis: {
        type: "value",
        axisLine: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: "rgba(255, 255, 255, 0.1)"
          }
        },
        axisLabel: {
          color: "rgba(255, 255, 255, 0.6)",
          fontSize: 14 // Y轴左侧数值刻度标签字体大小
        },
        min: 0
      },
      series: [
        {
          name: "任务总数",
          type: "bar",
          data: totalData,
          itemStyle: {
            color: "#409EFF"
          },
          barWidth: currentRange.value === "month" ? 8 : 12, // 动态适应：月模式柱宽8px，年模式柱宽12px
          barGap: "20%", // 两个柱子之间的间距
          barCategoryGap: "40%" // 不同类别之间的间距
        },
        {
          name: "总完成数",
          type: "bar",
          data: completedData,
          itemStyle: {
            color: "#67C23A"
          },
          barWidth: currentRange.value === "month" ? 8 : 12, // 动态适应：月模式柱宽8px，年模式柱宽12px
          barGap: "20%",
          barCategoryGap: "40%"
        }
      ]
    };

    chart.setOption(option);
  }
};

// 监听数据变化重新渲染图表
watch(() => props.data, renderChart, { deep: true });

// 监听窗口大小变化
const handleResize = () => {
  if (chart) {
    chart.resize();
  }
};

onMounted(() => {
  window.addEventListener("resize", handleResize);
  renderChart();
});

onUnmounted(() => {
  chart?.dispose();
  window.removeEventListener("resize", handleResize);
});

// 标题点击跳转
const handleStatisticsClick = () => {
  router.push({
    path: "/illegal/log",
    query: {
      from: "largeScreen"
    }
  });
};
</script>

<style lang="scss" scoped>
@import "../styles/font-sizes.scss";
.handle-statistics {
  display: flex;
  flex-direction: column;
  height: 100%;

  .panel-header {
    flex-shrink: 0;
    padding: 15px 16px 12px;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .title-text {
        position: relative;
        padding-left: 12px;
        @extend .fs-component-title; /* 组件标题"精准劝导概览"字体大小 */
        font-weight: 500;
        color: #fff;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          color: #409eff;
          transform: translateX(2px);
        }

        &::before {
          position: absolute;
          top: 50%;
          left: 0;
          width: 4px;
          height: 16px;
          content: "";
          background: #409eff;
          border-radius: 2px;
          transition: all 0.3s;
          transform: translateY(-50%);
        }

        &:hover::before {
          height: 20px;
          background: #66b1ff;
        }
      }

      .time-controls {
        display: flex;
        gap: 8px;
        align-items: center;
        margin-left: 20px;

        // picker-toggle 样式已移除，现在使用 TimeRangeSelector 组件
      }
    }
  }

  .stat-info {
    display: flex;
    flex-shrink: 0;
    gap: 16px;
    padding: 0 16px;

    .info-group {
      display: flex;
      flex: 1;
      gap: 16px;

      .info-item {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        background: rgb(255 255 255 / 5%);
        border-radius: 6px;
        transition: all 0.3s ease;

        .label-box {
          display: flex;
          gap: 12px;
          align-items: center;

          .icon-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            overflow: hidden;
            border-radius: 50%;
            transition: all 0.3s ease;

            &::before {
              position: absolute;
              inset: 0;
              padding: 2px;
              content: "";
              background: linear-gradient(
                45deg,
                var(--gradient-from),
                var(--gradient-to)
              );
              border-radius: 50%;
              mask:
                linear-gradient(#fff 0 0) content-box,
                linear-gradient(#fff 0 0);
              mask:
                linear-gradient(#fff 0 0) content-box,
                linear-gradient(#fff 0 0);
              mask-composite: xor;
              mask-composite: exclude;
            }

            &::after {
              position: absolute;
              inset: 4px;
              content: "";
              background: radial-gradient(
                circle at 30% 30%,
                var(--gradient-to),
                transparent 70%
              );
              filter: blur(2px);
              border-radius: 50%;
              opacity: 0.2;
            }

            .icon {
              z-index: 1;
              font-size: 20px; /* 统计信息区域图标字体大小 */
              filter: drop-shadow(0 2px 4px rgb(0 0 0 / 20%));
              transition: all 0.3s ease;
            }

            &.warning {
              --gradient-from: #e6a23c;
              --gradient-to: #f5deb3;

              background: rgb(230 162 60 / 10%);
              box-shadow: 0 0 20px rgb(230 162 60 / 20%);

              .icon {
                color: #e6a23c;
              }
            }

            &.success {
              --gradient-from: #67c23a;
              --gradient-to: #95eb6a;

              background: rgb(103 194 58 / 10%);
              box-shadow: 0 0 20px rgb(103 194 58 / 20%);

              .icon {
                color: #67c23a;
              }
            }

            &:hover {
              transform: translateY(-2px);

              .icon {
                transform: scale(1.1);
              }

              &::after {
                filter: blur(4px);
                opacity: 0.4;
              }
            }
          }
        }

        .label {
          @extend .fs-persuasion-value; /* 统计信息标签("任务总数"、"总完成数")字体大小 */
          color: rgb(255 255 255 / 60%);
        }

        .value {
          @extend .fs-persuasion-value-number; /* 统计数值字体大小 */
          font-weight: bold;
          color: transparent;
          background: linear-gradient(to bottom, #fff, #7eb9ff);
          background-clip: text;

          &.success {
            color: transparent;
            background: linear-gradient(to bottom, #fff, #67c23a);
            background-clip: text;
          }
        }
      }
    }
  }

  .stat-chart {
    flex: 1;
    height: 240px;
    min-height: 200px;
    padding: 0 16px 16px;
  }
}

// Element Plus 组件主题适配

:deep(.el-select-dropdown) {
  background: rgb(30 39 74 / 95%);
  border: 1px solid rgb(64 158 255 / 20%);
}

:deep(.el-select-dropdown__item) {
  color: rgb(255 255 255 / 80%);

  &:hover {
    background: rgb(64 158 255 / 20%);
  }

  &.selected {
    color: #409eff;
    background: rgb(64 158 255 / 10%);
  }
}

// Element Plus 弹出框样式 - 使用全局样式
</style>
