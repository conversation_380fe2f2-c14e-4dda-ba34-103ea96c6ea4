<template>
  <!-- 地图 -->
  <div class="map-view">
    <div id="map-container" ref="mapRef" />
    <div v-show="!videoDialogVisible && !dialogVisible" class="map-legend">
      <div class="legend-item">
        <span class="legend-dot" style="background-color: #67c23a" />
        <span class="legend-text">在岗</span>
      </div>
      <div class="legend-item">
        <span class="legend-dot" style="background-color: #faad14" />
        <span class="legend-text">脱岗</span>
      </div>
      <div class="legend-item">
        <span class="legend-dot" style="background-color: #909399" />
        <span class="legend-text">休息</span>
      </div>
      <div class="legend-item">
        <span class="legend-dot" style="background-color: #f56c6c" />
        <span class="legend-text">故障</span>
      </div>
    </div>
    <VideoDialog
      :visible="videoDialogVisible"
      :devices="videoDevices"
      @update:visible="handleVideoClose"
    />
    <PointDetailDialog
      v-if="dialogVisible"
      v-model:visible="dialogVisible"
      :title="dialogTitle"
      :pointData="dialogData"
    />
  </div>
</template>

<script setup lang="ts">
import { confirmAlarm, getIntersection, getManualAlarmList } from "@/api/alarm";
import { useAppStoreHook } from "@/store/modules/app";
import type { EChartsOption } from "echarts";
import * as echarts from "echarts";
import "echarts/extension/bmap/bmap";
import { ElMessage } from "element-plus";
import { onBeforeUnmount, onMounted, ref, watch } from "vue";
import PointDetailDialog from "../dialogs/PointDetailDialog.vue";
import { ManualAlarm } from "../types";
import VideoDialog from "./VideoDialog.vue";
import {
  getDistrictBoundary,
  getDistrictStyle,
  districtCenters
} from "@/utils/yibinBoundary";
import {
  getYibinCityBoundary,
  getYibinCityStyle
} from "@/utils/yibinCityBoundary";
import type { Point, PointData } from "../types/map";

// 添加类型声明
declare global {
  interface Window {
    BMap: any;
    BMapGL: any;
  }
}

// 手动报警列表
const manualAlarmList = ref<ManualAlarm[]>([]);
const appStore = useAppStoreHook();

const props = withDefaults(
  defineProps<{
    points?: Point[];
  }>(),
  {
    points: () => []
  }
);

const emit = defineEmits<{
  (e: "pointClick", point: Point): void;
}>();

const mapRef = ref<HTMLElement>();
let chart: echarts.ECharts | null = null;

// 添加轮询相关的代码
let pollingTimer: NodeJS.Timeout | null = null;

// 视频对话框相关
const videoDialogVisible = ref(false);
const videoDevices = ref<
  Array<{
    id: number;
    deviceName: string;
    streamKey: string;
    equipmentNumber: string;
  }>
>([]);

// 当前报警ID
const currentAlarmId = ref<number>();
const isConfirming = ref(false);

// 添加对话框相关的响应式变量
const dialogVisible = ref(false);
const dialogTitle = ref("");
const dialogData = ref<PointData>({
  site: "",
  address: "",
  city: "",
  county: "",
  township: "",
  hamlet: "",
  devices: [],
  schedules: [],
  staff: [],
  status: ""
});

// 添加响应式变量存储地图中心点
const mapCenter = ref([104.641752, 28.772917]); // 默认中心点

// 处理报警点击事件
const handleAlarmClick = async (alarm: ManualAlarm) => {
  try {
    // 获取路口视频地址
    const res = await getIntersection(alarm.id);
    if (res.code === 200) {
      if (!Array.isArray(res.data)) {
        ElMessage.warning("返回数据格式错误");
        return;
      }
      // 动态处理所有返回的设备
      videoDevices.value = res.data.map(device => ({
        id: device.id,
        deviceName: device.deviceName,
        streamKey: device.streamKey,
        equipmentNumber: device.equipmentNumber
      }));

      videoDialogVisible.value = true;
      currentAlarmId.value = alarm.id;
    } else {
      ElMessage.warning("未获取到视频地址");
    }
  } catch (error) {
    // 只保留一个错误通知，避免重复显示
    ElMessage.error("获取视频地址失败");
    // 仍保留错误日志，但不会影响用户界面显示
    console.error("获取视频地址失败详情:", error);
  }
};

// 处理视频对话框关闭
const handleVideoClose = async (visible: boolean) => {
  videoDialogVisible.value = visible;
  if (!visible && currentAlarmId.value && !isConfirming.value) {
    isConfirming.value = true;
    try {
      const res = await confirmAlarm(currentAlarmId.value);
      if (res.code === 200) {
        ElMessage.success("已确认报警");
        // 关闭对应的报警通知
        const notification = document.querySelector(
          `div[data-alarm-id="${currentAlarmId.value}"]`
        );
        if (notification) {
          notification.remove();
        }
      }
    } catch (error) {
      // 只保留一个错误通知，避免重复显示
      ElMessage.error("确认报警失败");
      // 仍保留错误日志，但不会影响用户界面显示
      console.error("确认报警失败详情:", error);
    } finally {
      currentAlarmId.value = undefined;
      isConfirming.value = false;
    }
  }
};

const fetchMapData = () => {
  if (!mapRef.value) return;
  getManualAlarmList(appStore.getLargeScreenArea).then(res => {
    if (res.code === 200) {
      manualAlarmList.value = res.data;
      // 显示报警通知
      if (manualAlarmList.value.length > 0) {
        if (!videoDialogVisible.value)
          handleAlarmClick(manualAlarmList.value[0]);
      }
    }
  });
};

// 开始轮询
const startPolling = () => {
  // 立即执行一次
  fetchMapData();

  // 设置 10s 的轮询间隔
  pollingTimer = setInterval(() => {
    fetchMapData();
  }, 10000);
};

// 停止轮询
const stopPolling = () => {
  if (pollingTimer) {
    clearInterval(pollingTimer);
    pollingTimer = null;
  }
};

// 修改渲染地图的代码
const renderMap = () => {
  if (!mapRef.value) return;

  if (!chart) {
    chart = echarts.init(mapRef.value);
  }

  try {
    // 计算所有点位的边界框，用于自动缩放
    const calculateBounds = () => {
      if (!props.points || props.points.length === 0) {
        return {
          minLng: mapCenter.value[0] - 0.05,
          maxLng: mapCenter.value[0] + 0.05,
          minLat: mapCenter.value[1] - 0.05,
          maxLat: mapCenter.value[1] + 0.05,
          zoom: 14
        };
      }

      // 初始化边界值
      let minLng = 180,
        maxLng = -180,
        minLat = 90,
        maxLat = -90;

      // 遍历所有点位，找出最大和最小的经纬度
      props.points.forEach(point => {
        const lng = Number(point.coordinates[0]);
        const lat = Number(point.coordinates[1]);

        if (!isNaN(lng) && !isNaN(lat)) {
          minLng = Math.min(minLng, lng);
          maxLng = Math.max(maxLng, lng);
          minLat = Math.min(minLat, lat);
          maxLat = Math.max(maxLat, lat);
        }
      });

      // 根据点位数量和分布确定缩放级别
      let zoom = 14; // 默认缩放级别
      const lngDiff = maxLng - minLng;
      const latDiff = maxLat - minLat;

      // 根据经纬度差值计算合适的缩放级别
      if (lngDiff > 0.5 || latDiff > 0.5) {
        zoom = 10; // 大范围分布
      } else if (lngDiff > 0.2 || latDiff > 0.2) {
        zoom = 12; // 中等范围
      } else if (lngDiff > 0.05 || latDiff > 0.05) {
        zoom = 14; // 小范围
      } else {
        zoom = 16; // 集中分布
      }

      return {
        minLng,
        maxLng,
        minLat,
        maxLat,
        zoom
      };
    };

    // 获取地图边界和缩放级别
    let centerLng, centerLat, zoomLevel;

    // 如果选择了特定区县，使用该区县的中心点
    const selectedCounty = appStore.getLargeScreenArea?.county;
    if (selectedCounty) {
      // 检查是否有该区县的中心点信息
      if (districtCenters[selectedCounty]) {
        centerLng = districtCenters[selectedCounty].lng;
        centerLat = districtCenters[selectedCounty].lat;
        zoomLevel = 12; // 选择特定区县时使用更大的缩放级别
      } else {
        // 如果没有找到区县中心点，使用计算的边界
        const bounds = calculateBounds();
        centerLng = (bounds.minLng + bounds.maxLng) / 2;
        centerLat = (bounds.minLat + bounds.maxLat) / 2;
        zoomLevel = bounds.zoom;
      }
    } else {
      // 未选择特定区县时，使用宜宾市中心
      centerLng = 104.641752; // 宜宾市中心经度
      centerLat = 28.772917; // 宜宾市中心纬度
      zoomLevel = 10; // 显示整个宜宾市时使用较小的缩放级别
    }

    const option: EChartsOption = {
      backgroundColor: "transparent",
      bmap: {
        center: [centerLng, centerLat],
        zoom: zoomLevel,
        roam: true,
        mapStyleV2: {
          styleId: "91ffadca1341e6dfc29fcaeabdf5ef5d"
        }
      },
      tooltip: {
        trigger: "item",
        backgroundColor: "rgba(0, 24, 75, 0.8)",
        borderColor: "rgba(0, 110, 255, 0.5)",
        borderWidth: 1,
        padding: [6, 8], // 减小内边距，让tooltip更紧凑
        // 优化tooltip位置，防止被遮挡
        confine: true, // 限制tooltip在图表容器内
        position: function (point, params, dom, rect, size) {
          // 智能位置计算，防止tooltip超出边界
          const [mouseX, mouseY] = point;
          const { contentSize, viewSize } = size;
          const [tooltipWidth, tooltipHeight] = contentSize;
          const [containerWidth, containerHeight] = viewSize;

          let x = mouseX + 10; // 默认在鼠标右侧
          let y = mouseY - tooltipHeight / 2; // 垂直居中

          // 检查右边界，如果超出则显示在左侧
          if (x + tooltipWidth > containerWidth) {
            x = mouseX - tooltipWidth - 10;
          }

          // 检查上下边界
          if (y < 0) {
            y = 10;
          } else if (y + tooltipHeight > containerHeight) {
            y = containerHeight - tooltipHeight - 10;
          }

          return [x, y];
        },
        formatter: (params: any) => {
          if (!params.data || !params.data.data) return "";

          const data = params.data.data;
          if (!data.devices) return "";

          const onlineCount = data.devices.filter(d => d.status === 0).length;

          let devicesHtml = data.devices
            .map(
              device => `
            <div style="margin-top: 2px;padding-left: 8px;font-size:11px;line-height:1.2;">
              ${device.name} (${device.type}):
              <span style="color:${device.status === 0 ? "#67c23a" : "#909399"}">
                ${device.status === 0 ? "在线" : "故障"}
              </span>
            </div>
          `
            )
            .join("");

          let staffHtml = data.staff
            .map(
              staff => `
            <div style="margin-top: 2px;padding-left: 8px;font-size:11px;line-height:1.2;">
              ${staff.name} (${staff.deptName}): ${staff.phone}
            </div>
          `
            )
            .join("");

          // let scheduleHtml = data.schedules
          //   .map(
          //     schedule => `
          //   <div style="margin-top: 3px;padding-left: 10px;font-size:12px;">
          //     ${schedule.userName}:
          //     ${schedule.shifts
          //       .map(
          //         shift =>
          //           `${shift.shiftName}(${shift.startTime}-${shift.endTime})`
          //       )
          //       .join(", ")}
          //   </div>
          // `
          //   )
          //   .join("");

          return `
            <div style="font-size: 14px; color: #fff;">
              <div style="margin-bottom: 5px;font-weight:bold;">${data.address}</div>
              <div style="margin-bottom: 3px;">设备状态：${onlineCount}/${data.devices.length} 在线</div>
              <div style="margin-bottom: 3px;font-weight:bold;">设备列表：</div>
              ${devicesHtml}
              <div style="margin-bottom: 3px;font-weight:bold;">人员列表：</div>
              ${staffHtml}
            </div>
          `;
        }
      },
      series: [
        // 添加区域边界 - 根据选择的区县显示边界
        {
          name: "区域边界",
          type: "lines",
          coordinateSystem: "bmap",
          polyline: true,
          data: getBoundaryDataBySelection(),
          effect: {
            show: true,
            period: 6,
            trailLength: 0,
            symbol: "rect",
            symbolSize: 2
          },
          zlevel: 1
        },
        // 添加区县标签
        {
          name: "区县标签",
          type: "scatter",
          coordinateSystem: "bmap",
          data: Object.entries(districtCenters)
            .filter(([name]) => {
              // 获取appStore中的区县信息
              const selectedCounty = appStore.getLargeScreenArea?.county;

              // 如果选择了特定区县，只显示该区县的标签
              if (selectedCounty) {
                return name === selectedCounty;
              }

              // 未选择区县时不显示任何区县标签
              return false;
            })
            .map(([name, center]: [string, { lng: number; lat: number }]) => ({
              name,
              value: [center.lng, center.lat],
              itemStyle: {
                opacity: 0 // 使点不可见，只显示标签
              }
            })),
          label: {
            show: true,
            formatter: "{b}",
            position: "inside" as const,
            backgroundColor: "rgba(0,0,0,0.5)",
            padding: [4, 8],
            borderRadius: 4,
            color: "#fff",
            fontSize: 14,
            fontWeight: "bold"
          },
          emphasis: {
            label: {
              color: "#fff",
              fontSize: 16,
              fontWeight: "bold",
              backgroundColor: "rgba(0,24,75,0.8)"
            }
          },
          zlevel: 2
        },
        // 点位标记
        {
          name: "点位",
          type: "effectScatter",
          coordinateSystem: "bmap",
          data:
            props.points?.map(point => ({
              name: point.data.hamlet,
              value: [
                Number(point.coordinates[0]),
                Number(point.coordinates[1])
              ],
              data: point.data,
              itemStyle: {
                color: getPointColor(point)
              }
            })) || [],
          symbolSize: 15,
          showEffectOn: "render",
          rippleEffect: {
            brushType: "stroke",
            scale: 2.5,
            period: 4
          },
          label: {
            show: true,
            position: "top",
            formatter: "{b}",
            color: "#fff",
            fontSize: 12,
            distance: 10
          },
          emphasis: {
            scale: 1.5,
            label: {
              show: true,
              color: "#fff",
              fontSize: 14
            }
          },
          zlevel: 2
        }
      ]
    };

    // 使用 setOption 的第二个参数来控制更新行为
    chart.setOption(option, {
      notMerge: false,
      replaceMerge: ["bmap"]
    });

    // 修改为单击事件监听
    chart.off("click");
    chart.on("click", params => {
      if (
        params.componentType === "series" &&
        params.componentSubType === "effectScatter" &&
        params.seriesName === "点位" &&
        params.dataIndex != null
      ) {
        const point = props.points?.[params.dataIndex];
        if (point) {
          dialogTitle.value = point.data.site;
          dialogData.value = point.data;
          dialogVisible.value = true;
        }
      }
    });
  } catch (error) {
    console.error("渲染地图时出错:", error);
  }
};

// 修改 updateMapCenter 方法
const updateMapCenter = async () => {
  try {
    // 检查是否有城市信息
    if (!appStore.getLargeScreenArea?.county) {
      console.warn("未获取到区域信息");
      return;
    }

    // 不再调用API获取坐标，直接使用本地坐标数据
    const selectedCounty = appStore.getLargeScreenArea?.county;

    if (selectedCounty && districtCenters[selectedCounty]) {
      // 使用本地存储的中心点数据
      const longitude = districtCenters[selectedCounty].lng;
      const latitude = districtCenters[selectedCounty].lat;

      mapCenter.value = [longitude, latitude];

      // 使用 setOption 更新地图
      if (chart) {
        // 如果点位数量大于0，则重新渲染地图以自动计算缩放级别
        if (props.points && props.points.length > 0) {
          renderMap();
        } else {
          // 如果没有点位，只更新中心点
          chart.setOption({
            bmap: {
              center: mapCenter.value
            }
          });
        }
      }
    } else {
      // 如果没有找到区县中心点，使用宜宾市中心
      mapCenter.value = [104.641752, 28.772917]; // 宜宾市中心
    }
  } catch (error) {
    console.error("获取地图中心点失败:", error);
  }
};

// 修改 watch 配置，添加深度监听
watch(
  () => appStore.getLargeScreenArea,
  () => {
    updateMapCenter();
  },
  { immediate: true, deep: true }
);

// 自动更新数据
watch(
  () => props.points,
  () => {
    renderMap();
  },
  { deep: true }
);

// 监听区县选择变化
watch(
  () => appStore.getLargeScreenArea,
  () => {
    renderMap();
  },
  { deep: true }
);

// 监听窗口大小变化
const handleResize = () => {
  chart?.resize();
};

onMounted(() => {
  renderMap();
  window.addEventListener("resize", handleResize);
  // 启动报警轮询
  startPolling();
});

onBeforeUnmount(() => {
  chart?.dispose();
  window.removeEventListener("resize", handleResize);
  // 清理报警轮询
  stopPolling();
});

// 获取点位颜色
const getPointColor = (point: Point) => {
  const { status } = point.data;
  switch (status) {
    case "故障":
      return "#f56c6c"; // 红色
    case "脱岗":
      return "#faad14"; // 黄色
    case "休息":
      return "#909399"; // 灰色
    default:
      return "#67c23a"; // 绿色（在岗）
  }
};

// 添加边界数据处理函数
// 根据选择的区县返回边界数据
const getBoundaryDataBySelection = () => {
  // 所有区县名称
  const allDistricts = [
    "翠屏区",
    "南溪区",
    "叙州区",
    "江安县",
    "长宁县",
    "高县",
    "珙县",
    "筠连县",
    "兴文县",
    "屏山县"
  ];

  // 使用appStore中的区县信息
  const selectedCounty = appStore.getLargeScreenArea?.county;

  // 如果选择了特定区县
  if (selectedCounty) {
    // 检查选择的区县是否在列表中
    if (allDistricts.includes(selectedCounty)) {
      return [
        {
          name: selectedCounty,
          coords: getDistrictBoundary(selectedCounty),
          ...getDistrictStyle(selectedCounty)
        }
      ];
    }
  }

  // 如果是宜宾市总体（没有选择具体区县），显示宜宾市整体边界
  return [
    {
      name: "宜宾市",
      coords: getYibinCityBoundary()[0],
      ...getYibinCityStyle()
    }
  ];
};
</script>

<style lang="scss">
.map-view {
  position: relative;
  height: 100%;

  #map-container {
    height: 100%;
    background: rgb(0 21 41 / 40%);
  }

  .map-legend {
    position: absolute;
    right: 20px;
    bottom: 20px;
    z-index: 1000;
    padding: 10px;
    background: rgb(0 21 41 / 70%);
    border-radius: 4px;

    .legend-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .legend-dot {
        width: 10px;
        height: 10px;
        margin-right: 8px;
        border-radius: 50%;
      }

      .legend-text {
        font-size: 14px; // 地图图例文字大小
        color: rgb(255 255 255 / 80%);
      }
    }
  }
}

.tech-notification {
  @keyframes warningBlink {
    0% {
      box-shadow: 0 0 15px rgb(255 85 0 / 10%);
    }

    50% {
      box-shadow: 0 0 25px rgb(255 85 0 / 20%);
    }

    100% {
      box-shadow: 0 0 15px rgb(255 85 0 / 10%);
    }
  }

  @keyframes warningShake {
    0%,
    100% {
      transform: rotate(0);
    }

    25% {
      transform: rotate(-8deg);
    }

    75% {
      transform: rotate(8deg);
    }
  }

  position: absolute;
  left: 10px;
  padding: 24px !important;
  margin: 8px !important;
  overflow: hidden;
  background: rgb(24 28 47 / 80%) !important;
  backdrop-filter: blur(12px);
  border: 1px solid rgb(255 85 0 / 20%) !important;
  border-radius: 8px !important;
  box-shadow: 0 0 20px rgb(255 85 0 / 15%) !important;
  animation: warningBlink 3s ease-in-out infinite;

  .el-notification__group {
    margin: 0;
  }

  &::before {
    position: absolute;
    inset: 0;
    z-index: 0;
    content: "";
    background: linear-gradient(135deg, rgb(255 85 0 / 10%), transparent 60%);
  }

  .el-notification__title {
    position: relative;
    z-index: 1;
    display: flex;
    gap: 8px;
    align-items: center;
    padding-bottom: 8px !important;
    margin-bottom: 16px !important;
    font-size: 24px !important;
    font-weight: 500 !important;
    color: #f50 !important;
    border-bottom: 1px solid rgb(255 255 255 / 10%) !important;

    i {
      font-size: 24px;
      color: #f50;
      animation: warningShake 1s ease-in-out infinite;
    }
  }

  .el-notification__content {
    position: relative;
    z-index: 1;
    font-size: 22px !important;
    color: rgb(255 255 255 / 90%) !important;
  }

  .alarm-content {
    padding: 12px 0;

    .alarm-title {
      display: flex;
      gap: 8px;
      align-items: center;
      margin-bottom: 8px;
      font-size: 22px;
      font-weight: 500;
      color: rgb(255 255 255 / 95%);

      i {
        font-size: 22px;
        color: #f50;
        animation: warningShake 1s ease-in-out infinite;
      }
    }

    .alarm-info {
      position: relative;
      padding-left: 8px;
      margin-bottom: 5px;
      font-size: 20px;
      color: rgb(255 255 255 / 80%);
      transition: all 0.3s;
    }

    .alarm-tip {
      margin-top: 8px;
      font-size: 18px;
      color: #409eff;
      text-align: right;
      cursor: pointer;
      opacity: 0.8;
      transition: all 0.3s;
    }
  }
}
</style>
