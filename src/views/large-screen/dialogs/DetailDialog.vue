<template>
  <!-- 违法详情弹窗 -->
  <div v-if="visible" class="dialog-container" @click.self="closeDialog">
    <div class="dialog-content">
      <dv-border-box-12 class="dialog-border">
        <!-- 标题栏 -->
        <div class="dialog-header">
          <div class="title-section">
            <dv-decoration-5 style="width: 60px; height: 30px" />
            <div class="title-content">
              <h2 class="title-text">{{ locationTitle }}</h2>
              <span class="time-badge">{{ timeBadgeText }}</span>
            </div>
          </div>
          <div class="large-screen-close-btn" @click="closeDialog">
            <el-icon><Close /></el-icon>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="dialog-body">
          <!-- 控制面板 -->
          <div class="control-panel">
            <div class="area-selector">
              <AreaCascader
                v-model="selectedArea"
                :show-home-button="true"
                @change="handleAreaChange"
              />
            </div>
            <div class="time-selector">
              <TimeRangeSelector
                v-model="currentTimeRange"
                :enabled-ranges="['day', 'month', 'year']"
                :custom-labels="{ day: '日', month: '月', year: '年' }"
                :disable-future="true"
                :initial-date="selectedDate"
                :initial-month="selectedMonth"
                :initial-year="selectedYear"
                @change="handleTimeRangeChange"
                @date-change="handleDateChange"
                @month-change="handleMonthChange"
                @year-change="handleYearChange"
              />
            </div>
          </div>

          <!-- 数据展示区域 -->
          <div class="data-section">
            <!-- 统计卡片 -->
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-label">{{ timePrefix }}违法数</div>
                <div class="stat-value">
                  {{ statisticsData.totalViolations }}
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-label">{{ timePrefix }}下派数</div>
                <div class="stat-value">
                  {{ statisticsData.totalDispatched }}
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-label">{{ timePrefix }}劝导数</div>
                <div class="stat-value">
                  {{ statisticsData.totalPersuaded }}
                </div>
              </div>
            </div>

            <!-- 图表区域 -->
            <div class="chart-section">
              <h3 class="chart-title">{{ timePrefix }}违法类型统计</h3>
              <div
                ref="chartContainer"
                v-loading="loading"
                class="chart-container"
              />
            </div>
          </div>
        </div>

        <!-- 底部装饰 -->
        <dv-decoration-3 class="dialog-footer" />
      </dv-border-box-12>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from "vue";
import { Close } from "@element-plus/icons-vue";
import * as echarts from "echarts";
import dayjs from "dayjs";
import { useUserStoreHook } from "@/store/modules/user";
import AreaCascader from "@/components/AreaCascader.vue";
import TimeRangeSelector, {
  type TimeRangeType
} from "@/components/TimeRangeSelector.vue";
import {
  getPointDetailDataByDay,
  getPointDetailDataByMonth,
  getPointDetailDataByYear
} from "@/api/largeScreen";

// Props 定义
interface Props {
  visible: boolean;
  data?: any;
  timeType?: "day" | "month" | "year";
  // 外部传入的时间值
  selectedDate?: string;
  selectedMonth?: string;
  selectedYear?: string;
  // 外部传入的地区信息
  city?: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
  // 初始值
  initialMode?: "day" | "month" | "year";
  initialDate?: string;
  initialMonth?: string;
  initialYear?: string;
}

const props = withDefaults(defineProps<Props>(), {
  timeType: "day",
  initialMode: "day"
});

// Emits 定义
const emit = defineEmits<{
  "update:visible": [value: boolean];
  close: [];
}>();

// Store
const userStore = useUserStoreHook();

// 响应式数据
const loading = ref(false);
const chartContainer = ref<HTMLElement>();
const selectedArea = ref<number[]>([]);
const currentTimeRange = ref<TimeRangeType>(
  props.initialMode || props.timeType || "day"
);
const selectedDate = ref(
  props.initialDate || props.selectedDate || dayjs().format("YYYY-MM-DD")
);
const selectedMonth = ref(
  props.initialMonth || props.selectedMonth || dayjs().format("YYYY-MM")
);
const selectedYear = ref(
  props.initialYear || props.selectedYear || dayjs().format("YYYY")
);
// 定义统计数据类型
interface StatisticsData {
  totalViolations: number;
  totalDispatched: number;
  totalPersuaded: number;
  typeDetails: ViolationTypeDetail[];
  location?: string;
}

// 定义违法类型详情类型
interface ViolationTypeDetail {
  type: string;
  violations?: number;
  totalCount?: number;
}

// 定义 API 响应类型
interface ApiResponse {
  code: number;
  data?: {
    totalViolations?: number;
    totalDispatched?: number;
    totalPersuaded?: number;
    typeDetails?: ViolationTypeDetail[];
    location?: string;
  };
}

const statisticsData = ref<StatisticsData>({
  totalViolations: 0,
  totalDispatched: 0,
  totalPersuaded: 0,
  typeDetails: [],
  location: undefined
});

// 图表实例
let chartInstance: echarts.ECharts | null = null;

// 计算属性
const locationTitle = computed(() => {
  return statisticsData.value?.location || props.data?.location || "地点详情";
});

const timeBadgeText = computed(() => {
  const timeMap = {
    day: "日统计",
    month: "月统计",
    year: "年统计"
  };
  return timeMap[currentTimeRange.value] || "统计";
});

const timePrefix = computed(() => {
  const prefixMap = {
    day: "当日",
    month: "当月",
    year: "当年"
  };
  return prefixMap[currentTimeRange.value] || "当前";
});

// 事件处理方法
const closeDialog = () => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  emit("update:visible", false);
  emit("close");
};

const handleAreaChange = (areaIds: number[]) => {
  selectedArea.value = areaIds;
  fetchData();
};

const handleTimeRangeChange = (range: TimeRangeType) => {
  currentTimeRange.value = range;
  fetchData();
};

const handleDateChange = (date: string) => {
  selectedDate.value = date;
  fetchData();
};

const handleMonthChange = (month: string) => {
  selectedMonth.value = month;
  fetchData();
};

const handleYearChange = (year: string) => {
  selectedYear.value = year;
  fetchData();
};

// 工具方法
const getAddressParams = (areaIds: number[]) => {
  const params: any = {
    city: userStore.userInfo.city
  };

  if (!areaIds?.length) return params;

  let currentNode = userStore.userRegionTree?.find(
    item => item.id === areaIds[0]
  );
  if (!currentNode) return params;

  const levels = ["county", "township", "hamlet", "site"] as const;
  for (let i = 0; i < areaIds.length; i++) {
    const id = areaIds[i + 1];
    if (!currentNode) break;

    const level = levels[i];
    params[level] = currentNode.label;
    currentNode = findNodeById(currentNode.childList, id);
  }
  return params;
};

const findNodeById = (nodes: any[] | null, id: number): any | null => {
  if (!nodes) return null;

  for (const node of nodes) {
    if (node.id === id) return node;
    const found = findNodeById(node.childList, id);
    if (found) return found;
  }
  return null;
};

// 数据获取方法
const fetchData = async () => {
  if (loading.value) return;

  loading.value = true;
  try {
    const addressParams = getAddressParams(selectedArea.value);
    let response: ApiResponse | undefined;

    switch (currentTimeRange.value) {
      case "day": {
        const dateObj = new Date(selectedDate.value);
        const year = dateObj.getFullYear();
        const month = dateObj.getMonth() + 1;
        const day = dateObj.getDate();
        response = (await getPointDetailDataByDay({
          ...addressParams,
          year,
          month,
          day
        })) as ApiResponse;
        break;
      }
      case "month": {
        const [yearStr, monthStr] = selectedMonth.value.split("-");
        const year = parseInt(yearStr);
        const month = parseInt(monthStr);
        response = (await getPointDetailDataByMonth({
          ...addressParams,
          year,
          month
        })) as ApiResponse;
        break;
      }
      case "year": {
        const year = parseInt(selectedYear.value);
        response = (await getPointDetailDataByYear({
          ...addressParams,
          year
        })) as ApiResponse;
        break;
      }
    }

    if (response?.code === 200 && response.data) {
      statisticsData.value = {
        totalViolations: response.data.totalViolations || 0,
        totalDispatched: response.data.totalDispatched || 0,
        totalPersuaded: response.data.totalPersuaded || 0,
        typeDetails: response.data.typeDetails || [],
        location: response.data.location
      };
      await nextTick();
      renderChart();
    }
  } catch (error) {
    console.error("获取数据失败:", error);
  } finally {
    loading.value = false;
  }
};

// 图表渲染方法
const renderChart = () => {
  if (!chartContainer.value || !statisticsData.value.typeDetails?.length)
    return;

  // 销毁旧图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 创建新图表实例
  chartInstance = echarts.init(chartContainer.value);

  // 处理图表数据
  const chartData = statisticsData.value.typeDetails.map(item => ({
    type: item.type || "未知类型",
    violations: Number(item.violations || item.totalCount || 0)
  }));

  // 按违法数量排序
  const sortedData = [...chartData].sort((a, b) => b.violations - a.violations);

  // 图表配置
  const chartOption = {
    tooltip: {
      trigger: "axis",
      axisPointer: { type: "shadow" },
      formatter: "{b}: {c}",
      backgroundColor: "rgba(0, 10, 30, 0.9)",
      borderColor: "rgba(64, 158, 255, 0.3)",
      textStyle: {
        color: "#fff",
        fontSize: 12 // ECharts图表鼠标悬停提示框文字大小
      },
      padding: [6, 8], // 减小内边距，让tooltip更紧凑
      // 优化tooltip位置，防止被遮挡
      confine: true, // 限制tooltip在图表容器内
      appendToBody: true, // 将tooltip添加到body，避免被对话框容器裁剪
      position: function (point, params, dom, rect, size) {
        // 智能位置计算，防止tooltip超出边界
        const [mouseX, mouseY] = point;
        const { contentSize, viewSize } = size;
        const [tooltipWidth, tooltipHeight] = contentSize;
        const [containerWidth, containerHeight] = viewSize;

        let x = mouseX + 10; // 默认在鼠标右侧
        let y = mouseY - tooltipHeight / 2; // 垂直居中

        // 检查右边界，如果超出则显示在左侧
        if (x + tooltipWidth > containerWidth) {
          x = mouseX - tooltipWidth - 10;
        }

        // 检查上下边界
        if (y < 0) {
          y = 10;
        } else if (y + tooltipHeight > containerHeight) {
          y = containerHeight - tooltipHeight - 10;
        }

        return [x, y];
      }
    },
    grid: {
      top: "8%", // 进一步减少顶部边距，让柱状图更高
      left: "5%",
      right: "5%",
      bottom: "5%", // 为两行标签预留足够空间
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: sortedData.map(item => item.type),
      axisLabel: {
        color: "rgba(255, 255, 255, 0.8)",
        fontSize: 20, // 图表X轴标签文字大小（违法类型名称）
        interval: 0,
        rotate: 0, // 不旋转标签
        // 自动换行处理长标签
        formatter: function (value) {
          // 如果标签长度超过6个字符，则分两行显示
          if (value.length > 6) {
            const mid = Math.ceil(value.length / 2);
            return value.substring(0, mid) + "\n" + value.substring(mid);
          }
          return value;
        }
      },
      axisLine: {
        lineStyle: { color: "rgba(255, 255, 255, 0.2)" }
      }
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "rgba(255, 255, 255, 0.8)",
        fontSize: 20 // 图表Y轴标签文字大小（数值刻度）
      },
      splitLine: {
        lineStyle: { color: "rgba(255, 255, 255, 0.1)" }
      },
      axisLine: { show: false }
    },
    series: [
      {
        type: "bar",
        data: sortedData.map(item => ({
          value: item.violations,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "#409EFF" },
              { offset: 1, color: "rgba(64, 158, 255, 0.3)" }
            ])
          }
        })),
        barWidth: "50%",
        label: {
          show: true,
          position: "top",
          color: "#fff",
          fontSize: 20, // 图表柱状图顶部数值标签文字大小
          fontWeight: "bold"
        }
      }
    ]
  };

  chartInstance.setOption(chartOption);
};

// 初始化地区选择
const initializeArea = () => {
  if (!userStore.userRegionTree?.length) return;

  // 优先使用 props 传入的地区信息
  const areaPath = getAreaPathFromProps();
  if (areaPath.length > 0) {
    selectedArea.value = areaPath;
  } else {
    // 使用默认节点
    const defaultNodeId = getDefaultNodeId();
    if (defaultNodeId) {
      selectedArea.value = [defaultNodeId];
    }
  }
};

const getAreaPathFromProps = (): number[] => {
  if (!userStore.userRegionTree?.length) return [];

  const path: number[] = [];
  const levels = [];
  if (props.county) levels.push({ prop: props.county, type: "county" });
  if (props.township) levels.push({ prop: props.township, type: "township" });
  if (props.hamlet) levels.push({ prop: props.hamlet, type: "hamlet" });
  if (props.site) levels.push({ prop: props.site, type: "site" });

  let currentNodes = userStore.userRegionTree;
  for (const level of levels) {
    const foundNode = currentNodes.find(node => node.label === level.prop);
    if (foundNode) {
      path.push(foundNode.id);
      currentNodes = foundNode.childList;
    } else {
      break;
    }
  }
  return path;
};

const getDefaultNodeId = () => {
  const highestRole = userStore.getHighestRoleCode;
  const defaultValue = userStore.userInfo[highestRole];

  const findNode = (nodes: any[]): number | null => {
    for (const node of nodes) {
      if (node.label === defaultValue) return node.id;
      if (node.childList?.length) {
        const found = findNode(node.childList);
        if (found) return found;
      }
    }
    return null;
  };
  return findNode(userStore.userRegionTree || []);
};

// 窗口大小变化处理
const handleResize = () => {
  chartInstance?.resize();
};

// 监听器
watch(
  () => props.visible,
  newVisible => {
    if (newVisible) {
      nextTick(() => {
        initializeArea();
        fetchData();
      });
    }
  },
  { immediate: true }
);

watch(
  () => userStore.userRegionTree,
  newTree => {
    if (newTree?.length && selectedArea.value.length === 0) {
      initializeArea();
    }
  },
  { immediate: true }
);

watch(
  () => [props.selectedDate, props.selectedMonth, props.selectedYear],
  ([newDate, newMonth, newYear]) => {
    if (newDate && newDate !== selectedDate.value) {
      selectedDate.value = newDate;
    }
    if (newMonth && newMonth !== selectedMonth.value) {
      selectedMonth.value = newMonth;
    }
    if (newYear && newYear !== selectedYear.value) {
      selectedYear.value = newYear;
    }
    if (props.visible) {
      fetchData();
    }
  }
);

watch(
  () => [props.city, props.county, props.township, props.hamlet, props.site],
  () => {
    if (userStore.userRegionTree?.length) {
      initializeArea();
    }
  },
  { immediate: true }
);

watch(
  () => props.timeType,
  newTimeType => {
    if (newTimeType && newTimeType !== currentTimeRange.value) {
      currentTimeRange.value = newTimeType;
    }
  },
  { immediate: true }
);

// 生命周期
onMounted(() => {
  window.addEventListener("resize", handleResize);
  userStore.initUserRegionTree();
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});
</script>

<style lang="scss" scoped>
@import "../styles/font-sizes.scss";

// 动画效果
@keyframes dialogFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 遮罩层
.dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background: rgb(0 10 30 / 80%);
  backdrop-filter: blur(8px);
}

// 对话框主体
.dialog-content {
  position: absolute; // 更改为绝对定位，保证居中
  left: 50%; // 居中定位
  top: 50%; // 居中定位
  transform: translate(-50%, -50%) scale(var(--scale-ratio)); // 居中定位，保持大屏页面相同缩放比例
  width: 1792px; // 根据需求调整宽度，设计定稿宽度2560px，根据弹窗大小调整宽度，小于2560
  height: 1024px; // 根据需求调整高度，设计定稿高度1271px，根据弹窗大小调整高度，小于1271
  // animation: zoomIn 0.3s ease-out; // 取消窗口动画
}

// 边框容器
.dialog-border {
  width: 100%;
  height: 100%;
  padding: 24px;
  display: flex;
  flex-direction: column;
}

// 标题栏
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  flex-shrink: 0;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-text {
  @extend .fs-dialog-title; // 对话框标题文字大小
  font-weight: 600;
  color: #fff;
  margin: 0;
}

.time-badge {
  padding: 6px 12px;
  @extend .fs-dialog-title; // 时间标签文字大小（日统计/月统计/年统计）
  color: rgba(255, 255, 255, 0.9);
  background: rgba(64, 158, 255, 0.2);
  border: 1px solid rgba(64, 158, 255, 0.3);
  border-radius: 6px;
}

// 关闭按钮样式现在使用统一的 .large-screen-close-btn 类

// 内容区域
.dialog-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// 控制面板
.control-panel {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  margin-bottom: 20px;
  background: rgba(0, 24, 75, 0.4);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 8px;
  flex-shrink: 0;
}

.area-selector {
  flex: 0 0 auto;
  margin-right: 20px;
}

.time-selector {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

// 数据展示区域
.data-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// 统计卡片网格
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 24px;
  flex-shrink: 0;
}

.stat-card {
  padding: 20px;
  text-align: center;
  background: linear-gradient(
    135deg,
    rgba(20, 46, 94, 0.8),
    rgba(20, 46, 94, 0.6)
  );
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(64, 158, 255, 0.4);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
  }
}

.stat-label {
  @extend .fs-illegal-rank-card; // 统计卡片标签文字大小（当日违法数/当日下派数/当日劝导数）
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8px;
}

.stat-value {
  @extend .fs-illegal-rank-card-value; // 统计卡片数值文字大小（具体的数字）
  font-weight: bold;
  color: #409eff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

// 图表区域
.chart-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(20, 46, 94, 0.6);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 8px;
  padding: 20px;
  overflow: hidden;
}

.chart-title {
  @extend .fs-dialog-title; // 图表标题文字大小
  font-weight: 600;
  color: #fff;
  margin: 0 0 16px 0;
  padding-left: 12px;
  border-left: 4px solid #409eff;
}

.chart-container {
  flex: 1;
  min-height: 500px; // 设置合适的固定高度，让柱状图显示更高
}

// 底部装饰
.dialog-footer {
  width: 100%;
  height: 4px;
  margin-top: 16px;
  flex-shrink: 0;
}

// 滚动条样式
.dialog-body {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
  }
}
</style>
