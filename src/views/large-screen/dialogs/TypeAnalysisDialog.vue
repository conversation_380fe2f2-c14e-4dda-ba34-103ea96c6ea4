<template>
  <div v-if="dialogVisible" class="dialog-container" @click.self="handleClose">
    <div class="dialog-content">
      <dv-border-box-12 class="dialog-border">
        <!-- 标题栏 -->
        <div class="dialog-header">
          <div class="title">
            <dv-decoration-5 style="width: 60px; height: 30px" />
            <span class="title-text">违法类型分析</span>
          </div>
          <div class="large-screen-close-btn" @click="handleClose">
            <el-icon><Close /></el-icon>
          </div>
        </div>

        <!-- 控制栏 -->
        <div class="control-header">
          <!-- 左侧节点树选择器 -->
          <div class="dialog-area-selector">
            <AreaCascader
              ref="areaCascaderRef"
              v-model="selectedArea"
              :show-home-button="true"
              @change="handleAreaChange"
            />
          </div>

          <!-- 右侧时间选择器 -->
          <div class="header-right">
            <TimeRangeSelector
              v-model="selectedView"
              :enabled-ranges="['day', 'month', 'year']"
              :custom-labels="{ day: '日', month: '月', year: '年' }"
              :disable-future="true"
              :initial-date="selectedDate"
              :initial-month="selectedMonth"
              :initial-year="selectedYear"
              @change="handleTimeChange"
              @date-change="handleDateChange"
              @month-change="handleMonthChange"
              @year-change="handleYearChange"
            />
          </div>
        </div>

        <!-- 内容区 -->
        <div class="dialog-body">
          <!-- 下方左右结构 -->
          <div class="chart-legend-row">
            <div class="left-panel">
              <div class="chart-container">
                <div ref="dialogChartRef" class="chart" />
              </div>
            </div>
            <div class="right-panel">
              <div class="total-count">总数：{{ totalCount }}</div>
              <div class="legend-list">
                <div
                  v-for="item in chartData"
                  :key="item.name"
                  class="legend-item"
                >
                  <span
                    class="legend-color"
                    :style="{ background: item.itemStyle.color }"
                  />
                  <span class="legend-name">{{ item.name }}</span>
                  <span class="legend-value">{{ item.value }}次</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部装饰 -->
        <dv-decoration-3
          class="dialog-footer"
          style="width: 100%; height: 4px"
        />
      </dv-border-box-12>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from "vue";
import * as echarts from "echarts";
import { Close } from "@element-plus/icons-vue";
import { useAppStoreHook } from "@/store/modules/app";
import { useUserStoreHook } from "@/store/modules/user";
import AreaCascader from "@/components/AreaCascader.vue";
import TimeRangeSelector, {
  type TimeRangeType
} from "@/components/TimeRangeSelector.vue";
import dayjs from "dayjs";
import {
  getViolationTypeStatistics,
  getViolationTypeAnalysisByMonth,
  getViolationTypeAnalysisByYear
} from "@/api/screen";

const appStore = useAppStoreHook();
const userStore = useUserStoreHook();
const props = defineProps<{
  visible: boolean;
  city?: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
  // 大屏传递的初始时间值
  initialMode?: "day" | "month" | "year";
  initialDate?: string;
  initialMonth?: string;
  initialYear?: string;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
}>();

const dialogVisible = ref(false);
const dialogChartRef = ref<HTMLElement>();
let dialogChart: echarts.ECharts | null = null;

// 区域选择相关
const areaCascaderRef = ref();
const selectedArea = ref<number[]>([]);

// 时间选择相关 - 使用props传递的初始值或默认值
const selectedDate = ref(props.initialDate || dayjs().format("YYYY-MM-DD"));
const selectedMonth = ref(props.initialMonth || dayjs().format("YYYY-MM"));
const selectedYear = ref(
  props.initialYear ||
    (props.initialMonth
      ? props.initialMonth.split("-")[0]
      : dayjs().format("YYYY"))
);

// 添加 selectedView 状态 - 使用props传递的初始模式或默认值
const selectedView = ref<TimeRangeType>(
  (props.initialMode || "day") as TimeRangeType
);

// 时间范围当前选择
const currentRange = ref<TimeRangeType>(
  (props.initialMode || "day") as TimeRangeType
);

// 确保 selectedView 和 currentRange 保持同步
watch(selectedView, newVal => {
  currentRange.value = newVal;
});

// 监听props变化，同步更新内部状态
watch(
  () => props.initialMode,
  newMode => {
    if (newMode && newMode !== selectedView.value) {
      selectedView.value = newMode as TimeRangeType;
      currentRange.value = newMode as TimeRangeType;
    }
  },
  { immediate: true }
);

watch(
  () => props.initialDate,
  newDate => {
    if (newDate && newDate !== selectedDate.value) {
      selectedDate.value = newDate;
    }
  },
  { immediate: true }
);

watch(
  () => props.initialMonth,
  newMonth => {
    if (newMonth && newMonth !== selectedMonth.value) {
      selectedMonth.value = newMonth;
      // 同时更新年份
      selectedYear.value = newMonth.split("-")[0];
    }
  },
  { immediate: true }
);

watch(
  () => props.initialYear,
  newYear => {
    if (newYear && newYear !== selectedYear.value) {
      selectedYear.value = newYear;
    }
  },
  { immediate: true }
);

// 违法类型颜色映射
const violationColors = {
  超员: "#FF0000", // 红色
  未佩戴头盔: "#FFA500", // 橙色
  加装遮阳伞: "#FFFF00", // 黄色
  "超员+未佩戴头盔": "#00FF00", // 绿色
  "超员+加装遮阳伞": "#00FFFF", // 青色
  "未佩戴头盔+加装遮阳伞": "#0000FF", // 蓝色
  "超员+未佩戴头盔+加装遮阳伞": "#800080" // 紫色
};

// 添加固定的违法类型顺序
const violationTypes = [
  "超员",
  "未佩戴头盔",
  "加装遮阳伞",
  "超员+未佩戴头盔",
  "超员+加装遮阳伞",
  "未佩戴头盔+加装遮阳伞",
  "超员+未佩戴头盔+加装遮阳伞"
];

// 修改 fetchTypeData 函数
const fetchTypeData = async (range: TimeRangeType) => {
  try {
    let response;
    const areaParams = getAddressParams(selectedArea.value);

    switch (range) {
      case "day":
        response = await getViolationTypeStatistics({
          ...areaParams,
          date: selectedDate.value
        });
        break;
      case "month":
        response = await getViolationTypeAnalysisByMonth({
          ...areaParams,
          date: selectedMonth.value
        });
        break;
      case "year":
        response = await getViolationTypeAnalysisByYear({
          ...areaParams,
          year: selectedYear.value
        });
        break;
    }

    if (response.code === 200 && response.data) {
      // 创建一个Map来存储实际数据
      const dataMap = new Map(
        response.data.map(item => [item.name, item.value])
      );

      // 按照固定顺序生成数据，没有数据的显示为0
      return violationTypes.map(name => {
        const value = dataMap.get(name) || 0;
        const color = violationColors[name];
        return {
          name,
          value,
          itemStyle: {
            color
          },
          label: {
            color
          },
          labelLine: {
            lineStyle: {
              color
            }
          }
        };
      });
    }
    // 如果没有数据，返回所有类型为0的数据
    return violationTypes.map(name => ({
      name,
      value: 0,
      itemStyle: {
        color: violationColors[name]
      },
      label: {
        color: violationColors[name]
      },
      labelLine: {
        lineStyle: {
          color: violationColors[name]
        }
      }
    }));
  } catch (error) {
    // 发生错误时也返回所有类型为0的数据
    return violationTypes.map(name => ({
      name,
      value: 0,
      itemStyle: {
        color: violationColors[name]
      },
      label: {
        color: violationColors[name]
      },
      labelLine: {
        lineStyle: {
          color: violationColors[name]
        }
      }
    }));
  }
};

const totalCount = computed(() =>
  chartData.value.reduce((sum, item) => sum + (item.value || 0), 0)
);
const chartData = ref<any[]>([]);

// 修改 renderDialogChart 函数
const renderDialogChart = async () => {
  if (!dialogChartRef.value) return;

  // 只在图表实例不存在时创建
  if (!dialogChart) {
    dialogChart = echarts.init(dialogChartRef.value);
  }
  const data = await fetchTypeData(currentRange.value);
  // 保存原始数据，不过滤
  chartData.value = data;

  const dataCount = data.length;
  let baseLength = 32;
  if (dataCount <= 4) {
    baseLength = 32;
  } else if (dataCount <= 7) {
    baseLength = 28;
  } else {
    baseLength = 24;
  }

  const labelLineLength = baseLength;

  const option = {
    tooltip: {
      show: true,
      trigger: "item",
      formatter: "{b}: {c}次 ({d}%)",
      backgroundColor: "rgba(0, 24, 75, 0.8)",
      borderColor: "rgba(255, 255, 255, 0.2)",
      textStyle: {
        color: "#fff",
        fontSize: 12 // 减小字体大小，让tooltip更小巧
      },
      padding: [6, 8], // 减小内边距，让tooltip更紧凑
      // 优化tooltip位置，防止被遮挡
      confine: true, // 限制tooltip在图表容器内
      appendToBody: true, // 将tooltip添加到body，避免被对话框容器裁剪
      position: function (point, params, dom, rect, size) {
        // 智能位置计算，防止tooltip超出边界
        const [mouseX, mouseY] = point;
        const { contentSize, viewSize } = size;
        const [tooltipWidth, tooltipHeight] = contentSize;
        const [containerWidth, containerHeight] = viewSize;

        let x = mouseX + 10; // 默认在鼠标右侧
        let y = mouseY - tooltipHeight / 2; // 垂直居中

        // 检查右边界，如果超出则显示在左侧
        if (x + tooltipWidth > containerWidth) {
          x = mouseX - tooltipWidth - 10;
        }

        // 检查上下边界
        if (y < 0) {
          y = 10;
        } else if (y + tooltipHeight > containerHeight) {
          y = containerHeight - tooltipHeight - 10;
        }

        return [x, y];
      }
    },
    series: [
      {
        type: "pie",
        radius: ["25%", "60%"],
        center: ["50%", "55%"],
        width: "100%",
        height: "100%",
        itemStyle: {
          borderColor: "rgba(255, 255, 255, 0.3)",
          borderWidth: 2,
          shadowBlur: 20,
          shadowColor: "rgba(64,158,255,0.3)",
          borderRadius: 10
        },
        label: {
          alignTo: "edge",
          show: (params: any) => {
            return params.value > 0;
          },
          formatter: (params: any) => {
            return `{name|${params.name}}\n{percent|${params.percent}%}`;
          },
          minMargin: 16, // 增大标签最小间距，减少重叠
          edgeDistance: 32, // 增大标签与图形的距离，减少重叠
          lineHeight: 24, // 增大标签行高，减少重叠
          align: "right",
          rich: {
            name: {
              fontSize: 22, // ECharts 饼图标签 name 字体大小，已固定
              color: "#fff",
              align: "right"
            },
            percent: {
              fontSize: 22, // ECharts 饼图标签 percent 字体大小，已固定
              color: "rgba(255, 255, 255, 0.85)",
              align: "right"
            }
          }
        },
        labelLine: {
          length: 80, // 增大引导线长度，增加标签间距
          length2: 60, // 增大引导线第二段长度，增加标签间距
          maxSurfaceAngle: 80,
          show: (params: any) => {
            return params.value > 0;
          },
          smooth: true,
          lineStyle: {
            width: 2,
            color: (params: any) => {
              return params.color;
            },
            shadowColor: "rgba(64,158,255,0.3)",
            shadowBlur: 6
          }
        },
        emphasis: {
          labelLine: {
            lineStyle: {
              width: 3,
              color: "#fff"
            }
          }
        },
        // 只渲染 value > 0 的数据项
        data: chartData.value
          .filter(item => item.value > 0)
          .map(item => ({
            ...item,
            itemStyle: {
              ...item.itemStyle,
              opacity: 1
            }
          }))
      }
    ]
  };

  dialogChart.setOption(option);
};

// 初始化默认区域选择
const initializeAreaSelection = () => {
  // 如果用户地区树已加载且没有选中区域，使用默认值
  if (userStore.userRegionTree?.length && !selectedArea.value.length) {
    const defaultPathId = appStore.getLargeScreenPathId;
    if (defaultPathId?.length) {
      selectedArea.value = defaultPathId;
    }
  }
};

// 监听弹窗显示状态
watch(
  () => props.visible,
  async val => {
    dialogVisible.value = val;
    if (val) {
      // 确保用户地区树已初始化
      if (!userStore.userRegionTree?.length) {
        await userStore.initUserRegionTree();
      }

      // 初始化区域选择
      initializeAreaSelection();

      // 等待 DOM 更新后再初始化图表
      await nextTick();
      renderDialogChart();
    }
  },
  { immediate: true }
);

// 监听用户地区树变化
watch(
  () => userStore.userRegionTree,
  newTree => {
    if (newTree?.length && dialogVisible.value) {
      initializeAreaSelection();
    }
  },
  { immediate: true }
);

// 监听内部 dialogVisible 变化，同步到父组件
watch(dialogVisible, newVal => {
  emit("update:visible", newVal);
});

// 监听 props 变化，更新初始值
watch(
  () => [
    props.initialDate,
    props.initialMonth,
    props.initialYear,
    props.initialMode
  ],
  ([newDate, newMonth, newYear, newMode]) => {
    if (newDate) selectedDate.value = newDate;
    if (newMonth) selectedMonth.value = newMonth;
    if (newYear) selectedYear.value = newYear;
    if (newMode) {
      selectedView.value = newMode as TimeRangeType;
      currentRange.value = newMode as TimeRangeType;
    }
  },
  { immediate: true }
);

watch(
  () => dialogVisible.value,
  val => {
    emit("update:visible", val);
  }
);

// 修改窗口大小变化处理函数
const handleResize = () => {
  if (dialogChart) {
    dialogChart.resize();
  }
};

window.addEventListener("resize", handleResize);

onMounted(() => {
  if (dialogVisible.value) {
    renderDialogChart();
  }
});

onUnmounted(() => {
  if (dialogChart) {
    dialogChart.dispose();
    dialogChart = null;
  }
  window.removeEventListener("resize", handleResize);
});

const handleClose = () => {
  dialogVisible.value = false;
};

// 区域选择处理函数
const handleAreaChange = (value: number[]) => {
  selectedArea.value = value;
  renderDialogChart();
};

// 获取地址参数
const getAddressParams = (areaIds: number[]) => {
  const params = {
    city: userStore.userInfo.city
  } as any;

  if (!areaIds?.length) return params;

  let currentNode = userStore.userRegionTree.find(
    item => item.id === areaIds[0]
  );
  if (!currentNode) return params;

  const levels = ["county", "township", "hamlet", "site"] as const;
  for (let i = 0; i < areaIds.length; i++) {
    const id = areaIds[i + 1];
    if (!currentNode) break;

    const level = levels[i];
    params[level] = currentNode.label;
    currentNode = findNodeById(currentNode.childList, id);
  }

  return params;
};

// 根据ID查找节点
const findNodeById = (nodes: any[], id: number): any => {
  if (!nodes || !id) return undefined;

  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.childList) {
      const found = findNodeById(node.childList, id);
      if (found) return found;
    }
  }
  return undefined;
};

// 日期选择处理函数
const handleDateChange = (date: string) => {
  selectedDate.value = date;
  renderDialogChart();
};

// 月份选择处理函数
const handleMonthChange = (month: string) => {
  selectedMonth.value = month;
  renderDialogChart();
};

// 年份选择处理函数
const handleYearChange = (year: string) => {
  selectedYear.value = year;
  renderDialogChart();
};

// 添加时间切换方法
const handleTimeChange = (value: TimeRangeType) => {
  selectedView.value = value;
  currentRange.value = value;
  // 重新渲染图表以加载新数据
  renderDialogChart();
};
</script>

<style lang="scss" scoped>
@import "../styles/font-sizes.scss";
@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.5);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes numberScale {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// 移除响应式媒体查询，使用固定尺寸布局

.dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: rgb(0 10 30 / 80%);
  backdrop-filter: blur(8px);
}

.dialog-content {
  position: absolute; // 更改为绝对定位，保证居中
  left: 50%; // 居中定位
  top: 50%; // 居中定位
  transform: translate(-50%, -50%) scale(var(--scale-ratio)); // 居中定位，保持大屏页面相同缩放比例
  width: 1792px; // 根据需求调整宽度，设计定稿宽度2560px，根据弹窗大小调整宽度，小于2560
  height: 1024px; // 根据需求调整高度，设计定稿高度1271px，根据弹窗大小调整高度，小于1271
  // animation: zoomIn 0.3s ease-out; // 取消窗口动画
}

.dialog-border {
  width: 100%;
  height: 100%;
  padding: 30px;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;

  .title {
    display: flex;
    gap: 12px;
    align-items: center;

    .title-text {
      @extend .fs-dialog-title; // 标题栏字体大小
      font-weight: 500;
      color: #fff;
      text-shadow: 0 0 10px rgb(64 158 255 / 50%);
    }
  }

  // 关闭按钮样式现在使用统一的 .large-screen-close-btn 类
}

.dialog-body {
  height: calc(100% - 120px);
  padding: 20px;
  background: none;
}

// 控制栏样式
.control-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid rgb(64 158 255 / 20%);
}

// 日期选择器样式（右侧）
.date-picker-section {
  display: flex;
  flex-shrink: 0;
  justify-content: flex-end;
}

.chart-legend-row {
  display: flex;
  flex-direction: row;
  gap: 32px;
  align-items: stretch;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.left-panel {
  display: flex;
  flex: 1.2;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-width: 400px;

  .chart-container {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;

    .chart {
      width: 100%;
      height: 100%;
      background: linear-gradient(
        to bottom,
        rgb(0 24 75 / 20%),
        rgb(0 24 75 / 10%)
      );
      border-radius: 12px;
    }
  }
}

.right-panel {
  display: flex;
  flex: 0.8;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  min-width: 260px;

  .total-count {
    margin-bottom: 32px;
    @extend .fs-illegal-type-total; // 总数统计字体大小
    font-weight: bold;
    color: #fff;
    text-shadow: 0 0 10px rgb(64 158 255 / 50%);
  }

  .legend-list {
    display: flex;
    flex-direction: column;
    gap: 18px;
    width: 100%;

    .legend-item {
      display: flex;
      gap: 16px;
      align-items: center;
      @extend .fs-illegal-type-legend; // 图例项字体大小

      .legend-color {
        display: inline-block;
        width: 24px;
        height: 24px;
        margin-right: 8px;
        border-radius: 4px;
      }

      .legend-name {
        min-width: 80px;
        color: #fff;
      }

      .legend-value {
        margin-left: 8px;
        font-weight: 500;
        color: #409eff;
      }
    }
  }
}
</style>
