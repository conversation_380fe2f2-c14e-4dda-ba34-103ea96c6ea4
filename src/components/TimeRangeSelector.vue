<template>
  <div class="time-range-selector-container">
    <!-- 时间范围选择器 -->
    <div class="time-range-selector">
      <div
        v-for="item in enabledTimeRanges"
        :key="item.value"
        class="time-item"
        :class="{ active: selectedView === item.value }"
        @click="handleTimeChange(item.value)"
      >
        {{ item.label }}
      </div>
    </div>

    <!-- 时间选择器组合 -->
    <div class="time-picker-group">
      <CustomDatePicker
        v-if="
          selectedView === 'day' &&
          enabledTimeRanges.some(item => item.value === 'day')
        "
        v-model="selectedDate"
        placement="bottom"
        :disable-future="disableFuture"
        @change="handleDateChange"
      />
      <MonthPicker
        v-if="
          selectedView === 'month' &&
          enabledTimeRanges.some(item => item.value === 'month')
        "
        v-model="selectedMonth"
        placement="bottom"
        :disable-future="disableFuture"
        @change="handleMonthChange"
      />
      <YearPicker
        v-if="
          selectedView === 'year' &&
          enabledTimeRanges.some(item => item.value === 'year')
        "
        v-model="selectedYear"
        placement="bottom"
        :disable-future="disableFuture"
        @change="handleYearChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import CustomDatePicker from "@/components/CustomDatePicker.vue";
import MonthPicker from "@/components/MonthPicker.vue";
import YearPicker from "@/components/YearPicker.vue";
import dayjs from "dayjs";

// 定义时间范围类型
export type TimeRangeType = "day" | "month" | "year";

interface TimeRange {
  label: string;
  value: TimeRangeType;
}

interface Props {
  // 当前选中的视图
  modelValue?: TimeRangeType;
  // 启用的时间范围选项
  enabledRanges?: TimeRangeType[];
  // 自定义时间范围标签
  customLabels?: Partial<Record<TimeRangeType, string>>;
  // 是否禁用未来时间
  disableFuture?: boolean;
  // 初始日期值
  initialDate?: string;
  // 初始月份值
  initialMonth?: string;
  // 初始年份值
  initialYear?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "day",
  enabledRanges: () => ["day", "month", "year"],
  customLabels: () => ({}),
  disableFuture: true,
  initialDate: () => dayjs().format("YYYY-MM-DD"),
  initialMonth: () => dayjs().format("YYYY-MM"),
  initialYear: () => dayjs().format("YYYY")
});

const emit = defineEmits<{
  (e: "update:modelValue", value: TimeRangeType): void;
  (e: "change", value: TimeRangeType): void;
  (e: "dateChange", value: string): void;
  (e: "monthChange", value: string): void;
  (e: "yearChange", value: string): void;
}>();

// 内部状态
const selectedView = ref<TimeRangeType>(props.modelValue);
const selectedDate = ref(props.initialDate);
const selectedMonth = ref(props.initialMonth);
const selectedYear = ref(props.initialYear);

// 默认标签
const defaultLabels = {
  day: "日",
  month: "月",
  year: "年"
};

// 计算启用的时间范围选项
const enabledTimeRanges = computed<TimeRange[]>(() => {
  return props.enabledRanges.map(range => ({
    label: props.customLabels[range] || defaultLabels[range],
    value: range
  }));
});

// 监听外部传入的值
watch(
  () => props.modelValue,
  newVal => {
    selectedView.value = newVal;
  },
  { immediate: true }
);

// 监听内部值变化
watch(selectedView, newVal => {
  emit("update:modelValue", newVal);
});

// 处理时间范围切换
const handleTimeChange = (value: TimeRangeType) => {
  selectedView.value = value;
  emit("change", value);
};

// 处理日期变化
const handleDateChange = (date: string) => {
  selectedDate.value = date;
  emit("dateChange", date);
};

// 处理月份变化
const handleMonthChange = (month: string) => {
  selectedMonth.value = month;
  emit("monthChange", month);
};

// 处理年份变化
const handleYearChange = (year: string) => {
  selectedYear.value = year;
  emit("yearChange", year);
};

// 暴露方法和数据给父组件
defineExpose({
  selectedView,
  selectedDate,
  selectedMonth,
  selectedYear,
  handleTimeChange,
  handleDateChange,
  handleMonthChange,
  handleYearChange
});
</script>

<style lang="scss" scoped>
@import "../views/large-screen/styles/font-sizes.scss";
.time-range-selector-container {
  display: flex;
  gap: 12px;
  align-items: center;

  .time-range-selector {
    display: flex;
    height: 34px; /* 增加高度以适应更高的time-item */
    gap: 2px;
    padding: 1px; /* 减少padding */
    background: rgb(0 24 75 / 30%);
    border: 1px solid rgb(64 158 255 / 20%);
    border-radius: 4px;

    .time-item {
      padding: 4px 12px; /* 增加上下内边距 */
      height: 32px; /* 增加高度以适应更多的内边距 */
      @extend .fs-time-picker; // 组件时间选择器字体
      font-weight: normal;
      line-height: 24px; /* 调整行高，留出更多上下空间 */
      color: rgb(255 255 255 / 70%);
      cursor: pointer;
      border-radius: 2px;
      transition: all 0.3s;
      display: flex;
      align-items: center;
      justify-content: center;

      &.active {
        color: #fff;
        background: #409eff;
      }

      &:hover:not(.active) {
        color: #fff;
      }
    }
  }

  .time-picker-group {
    display: flex;
    gap: 12px;
    align-items: center;
  }
}

// 响应式设计
@media screen and (width <= 1366px) {
  .time-range-selector-container {
    gap: 8px;

    .time-range-selector {
      height: 32px; /* 调整高度以适应更高的time-item */
      gap: 1px;
      padding: 1px;

      .time-item {
        padding: 3px 8px; /* 增加上下内边距 */
        height: 30px; /* 增加高度 */
        @extend .fs-time-picker; // 组件时间选择器字体
        line-height: 24px; /* 调整行高，留出更多上下空间 */
      }
    }

    .time-picker-group {
      gap: 8px;
    }
  }
}
</style>
